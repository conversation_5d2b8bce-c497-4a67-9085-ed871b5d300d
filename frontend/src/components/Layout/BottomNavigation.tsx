import React from 'react';
import {
  Paper,
  BottomNavigation as <PERSON><PERSON><PERSON><PERSON>omNavigation,
  BottomNavigationAction,
  useTheme,
  Badge
} from '@mui/material';
import {
  QuestionAnswer as QuestionIcon,
  Quiz as QuizIcon,
  AutoAwesome as AutoAwesomeIcon,
  ViewCarousel as CardIcon,
  Event as EventIcon,
  AttachMoney as AttachMoneyIcon,
  Notifications as NotificationsIcon,
  Chat as ChatIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';

const BottomNavigation: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Get the current path
  const currentPath = location.pathname;

  // Determine which navigation item is active
  const getActiveValue = () => {
    if (user?.role === 'tutor') {
      if (currentPath.startsWith('/tutor/tutorials') || currentPath.startsWith('/tutor/sessions')) return 0;
      if (currentPath.startsWith('/tutor/booking-requests')) return 1;
      if (currentPath.startsWith('/chat')) return 2;
      if (currentPath.startsWith('/tutor/earnings')) return 3;
      return -1; // No default selection
    } else if (user?.role === 'admin') {
      if (currentPath.startsWith('/questions')) return 0;
      return -1; // No default selection
    } else {
      // Student navigation
      if (currentPath.startsWith('/mcq')) return 0;
      if (currentPath.startsWith('/tools')) return 1;
      if (currentPath.startsWith('/analytics')) return 2;
      if (currentPath.startsWith('/chat')) return 3;
      if (currentPath.startsWith('/flash-cards')) return 4;
      return -1; // No default selection
    }
  };

  return (
    <Paper
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        borderRadius: '16px 16px 0 0',
        overflow: 'hidden',
        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
        height: '64px',
      }}
      elevation={3}
    >
      <MuiBottomNavigation
        showLabels
        value={getActiveValue()}
        onChange={(_, newValue) => {
          if (user?.role === 'tutor') {
            switch(newValue) {
              case 0:
                navigate('/tutor/tutorials');
                break;
              case 1:
                navigate('/tutor/booking-requests');
                break;
              case 2:
                navigate('/chat');
                break;
              case 3:
                navigate('/tutor/earnings');
                break;
            }
          } else if (user?.role === 'admin') {
            switch(newValue) {
              case 0:
                navigate('/questions');
                break;
            }
          } else {
            // Student navigation
            switch(newValue) {
              case 0:
                navigate('/mcq');
                break;
              case 1:
                navigate('/tools');
                break;
              case 2:
                navigate('/analytics');
                break;
              case 3:
                navigate('/chat');
                break;
              case 4:
                navigate('/flash-cards/courses');
                break;
            }
          }
        }}
        sx={{
          height: '100%',
          '& .MuiBottomNavigationAction-root': {
            color: theme.palette.text.secondary,
            '&.Mui-selected': {
              color: theme.palette.primary.main,
            },
          },
        }}
      >
        {/* MCQ - Student only - Index 0 */}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="MCQ"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 0 ? 1.1 : 1,
                  y: getActiveValue() === 0 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <QuizIcon />
              </motion.div>
            }
          />
        )}

        {/* Tools - Student only - Index 1 */}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Tools"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 1 ? 1.1 : 1,
                  y: getActiveValue() === 1 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <AutoAwesomeIcon />
              </motion.div>
            }
          />
        )}

        {/* Analytics - Student only - Index 2 */}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Analytics"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 2 ? 1.1 : 1,
                  y: getActiveValue() === 2 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <AnalyticsIcon />
              </motion.div>
            }
          />
        )}

        {/* Chat - Student only - Index 3 */}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Chat"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 3 ? 1.1 : 1,
                  y: getActiveValue() === 3 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <ChatIcon />
              </motion.div>
            }
          />
        )}

        {/* Cards - Student only - Index 4 */}
        {user?.role === 'student' && (
          <BottomNavigationAction
            label="Cards"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 4 ? 1.1 : 1,
                  y: getActiveValue() === 4 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <CardIcon />
              </motion.div>
            }
          />
        )}

        {/* Tutorials - Tutor only - Index 0 */}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Tutorials"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 0 ? 1.1 : 1,
                  y: getActiveValue() === 0 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <EventIcon />
              </motion.div>
            }
          />
        )}

        {/* Requests - Tutor only - Index 1 */}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Requests"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 1 ? 1.1 : 1,
                  y: getActiveValue() === 1 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <NotificationsIcon />
              </motion.div>
            }
          />
        )}

        {/* Chat - Tutor only - Index 2 */}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Chat"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 2 ? 1.1 : 1,
                  y: getActiveValue() === 2 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <ChatIcon />
              </motion.div>
            }
          />
        )}

        {/* Earnings - Tutor only - Index 3 */}
        {user?.role === 'tutor' && (
          <BottomNavigationAction
            label="Earnings"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 3 ? 1.1 : 1,
                  y: getActiveValue() === 3 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <AttachMoneyIcon />
              </motion.div>
            }
          />
        )}

        {/* Questions - Admin only - Index 0 */}
        {user?.role === 'admin' && (
          <BottomNavigationAction
            label="Questions"
            icon={
              <motion.div
                animate={{
                  scale: getActiveValue() === 0 ? 1.1 : 1,
                  y: getActiveValue() === 0 ? -4 : 0
                }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                <QuestionIcon />
              </motion.div>
            }
          />
        )}
      </MuiBottomNavigation>
    </Paper>
  );
};

export default BottomNavigation;
