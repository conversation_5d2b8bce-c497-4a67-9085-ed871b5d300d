import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  useTheme,
  alpha,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Quiz as QuizIcon,
  AutoAwesome as ToolsIcon,
  Chat as ChatIcon,
  ViewCarousel as CardsIcon,
  Dashboard as DashboardIcon,
  ArrowBack as BackIcon,
  Home as HomeIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';

const DesktopNavigation: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Get current page info
  const getCurrentPageInfo = () => {
    const path = location.pathname;
    
    if (path.startsWith('/tools')) {
      return {
        title: 'Study Tools',
        subtitle: 'AI-powered learning tools',
        icon: <ToolsIcon />,
        color: theme.palette.primary.main,
        showBack: path !== '/tools'
      };
    } else if (path.startsWith('/mcq')) {
      return {
        title: 'MCQ Practice',
        subtitle: 'Multiple choice questions',
        icon: <QuizIcon />,
        color: theme.palette.info.main,
        showBack: path !== '/mcq'
      };
    } else if (path.startsWith('/chat')) {
      return {
        title: 'Chat',
        subtitle: 'Connect with tutors',
        icon: <ChatIcon />,
        color: theme.palette.success.main,
        showBack: false
      };
    } else if (path.startsWith('/flash-cards')) {
      return {
        title: 'Flashcards',
        subtitle: 'Study with flashcards',
        icon: <CardsIcon />,
        color: theme.palette.secondary.main,
        showBack: path !== '/flash-cards/courses'
      };
    } else if (path.startsWith('/analytics')) {
      return {
        title: 'Performance Analytics',
        subtitle: 'Track your learning progress',
        icon: <AnalyticsIcon />,
        color: theme.palette.warning.main,
        showBack: false
      };
    } else if (path === '/dashboard') {
      return {
        title: 'Dashboard',
        subtitle: 'Your learning overview',
        icon: <DashboardIcon />,
        color: theme.palette.primary.main,
        showBack: false
      };
    }
    
    return {
      title: 'CampusPQ',
      subtitle: 'Learning platform',
      icon: <HomeIcon />,
      color: theme.palette.primary.main,
      showBack: false
    };
  };

  const pageInfo = getCurrentPageInfo();

  // Navigation items for quick access
  const navItems = [
    {
      label: 'MCQ',
      path: '/mcq',
      icon: <QuizIcon />,
      color: theme.palette.info.main,
      active: location.pathname.startsWith('/mcq')
    },
    {
      label: 'Tools',
      path: '/tools',
      icon: <ToolsIcon />,
      color: theme.palette.primary.main,
      active: location.pathname.startsWith('/tools')
    },
    {
      label: 'Analytics',
      path: '/analytics',
      icon: <AnalyticsIcon />,
      color: theme.palette.warning.main,
      active: location.pathname.startsWith('/analytics')
    },
    {
      label: 'Chat',
      path: '/chat',
      icon: <ChatIcon />,
      color: theme.palette.success.main,
      active: location.pathname.startsWith('/chat')
    },
    {
      label: 'Cards',
      path: '/flash-cards/courses',
      icon: <CardsIcon />,
      color: theme.palette.secondary.main,
      active: location.pathname.startsWith('/flash-cards')
    }
  ];

  const handleBack = () => {
    const path = location.pathname;
    if (path.startsWith('/tools') && path !== '/tools') {
      navigate('/tools');
    } else if (path.startsWith('/mcq') && path !== '/mcq') {
      navigate('/mcq');
    } else if (path.startsWith('/flash-cards') && path !== '/flash-cards/courses') {
      navigate('/flash-cards/courses');
    } else if (path.startsWith('/analytics') && path !== '/analytics') {
      navigate('/analytics');
    } else {
      navigate('/dashboard');
    }
  };

  return (
    <Paper
      component={motion.div}
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.3 }}
      elevation={0}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        background: `linear-gradient(135deg, ${alpha(pageInfo.color, 0.05)} 0%, ${alpha(pageInfo.color, 0.02)} 100%)`,
        backdropFilter: 'blur(10px)',
        mb: 3,
        overflow: 'hidden'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 3,
          position: 'relative'
        }}
      >
        {/* Left side - Page info */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {pageInfo.showBack && (
            <Tooltip title="Go back">
              <IconButton
                onClick={handleBack}
                sx={{
                  bgcolor: alpha(pageInfo.color, 0.1),
                  color: pageInfo.color,
                  '&:hover': {
                    bgcolor: alpha(pageInfo.color, 0.2),
                    transform: 'scale(1.05)'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                <BackIcon />
              </IconButton>
            </Tooltip>
          )}
          
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: '50%',
              bgcolor: alpha(pageInfo.color, 0.1),
              color: pageInfo.color,
              border: `2px solid ${alpha(pageInfo.color, 0.2)}`
            }}
          >
            {pageInfo.icon}
          </Box>
          
          <Box>
            <Typography
              variant="h5"
              fontWeight="600"
              sx={{
                color: 'text.primary',
                mb: 0.5
              }}
            >
              {pageInfo.title}
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ fontSize: '0.875rem' }}
            >
              {pageInfo.subtitle}
            </Typography>
          </Box>
        </Box>

        {/* Right side - Quick navigation */}
        {user?.role === 'student' ? (
          <Box
            component={motion.div}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.3 }}
            sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}
          >
            {navItems.map((item, index) => (
              <Tooltip key={item.path} title={`Go to ${item.label}`}>
                <Button
                  component={motion.button}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 + index * 0.1, duration: 0.3 }}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  variant={item.active ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => navigate(item.path)}
                  startIcon={item.icon}
                  sx={{
                    borderRadius: 2,
                    px: 2,
                    py: 1,
                    minWidth: 'auto',
                    bgcolor: item.active ? item.color : 'transparent',
                    borderColor: item.active ? item.color : alpha(item.color, 0.3),
                    color: item.active ? 'white' : item.color,
                    '&:hover': {
                      bgcolor: item.active ? item.color : alpha(item.color, 0.1),
                      borderColor: item.color,
                      boxShadow: `0 6px 20px ${alpha(item.color, 0.4)}`
                    },
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    textTransform: 'none',
                    fontWeight: 600,
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': item.active ? {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: `linear-gradient(45deg, ${alpha(item.color, 0.1)}, ${alpha(item.color, 0.2)})`,
                      zIndex: -1
                    } : {}
                  }}
                >
                  {item.label}
                </Button>
              </Tooltip>
            ))}
          </Box>
        ) : (
          // For non-student users, show a simple welcome message
          <Box
            component={motion.div}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.3 }}
            sx={{ display: 'flex', alignItems: 'center' }}
          >
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{
                fontStyle: 'italic',
                opacity: 0.8
              }}
            >
              Welcome, {user?.full_name}
            </Typography>
          </Box>
        )}

        {/* Decorative gradient overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '2px',
            background: `linear-gradient(90deg, ${pageInfo.color} 0%, ${alpha(pageInfo.color, 0.5)} 50%, ${pageInfo.color} 100%)`
          }}
        />
      </Box>
    </Paper>
  );
};

export default DesktopNavigation;
